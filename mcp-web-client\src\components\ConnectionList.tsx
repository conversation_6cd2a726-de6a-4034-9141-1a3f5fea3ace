'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { useI18n } from '../contexts/I18nContext';
import { MCPConnection } from '@/types/mcp';

// Iconos SVG modernos
const LoadingIcon = () => (
  <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

const PlayIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1" />
  </svg>
);

const StopIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
  </svg>
);

const DeleteIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

const ServerIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
  </svg>
);

export function ConnectionList() {
  const {
    connections,
    activeConnection,
    connectToServer,
    disconnectFromServer,
    removeConnection,
    setActiveConnection
  } = useMCPClientContext();
  const { t } = useI18n();

  const [loadingConnections, setLoadingConnections] = useState<Set<string>>(new Set());

  const handleConnect = async (connectionId: string) => {
    setLoadingConnections(prev => new Set(prev).add(connectionId));
    try {
      await connectToServer(connectionId);
    } catch (error) {
      console.error('Error connecting:', error);
    } finally {
      setLoadingConnections(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  const handleDisconnect = async (connectionId: string) => {
    setLoadingConnections(prev => new Set(prev).add(connectionId));
    try {
      await disconnectFromServer(connectionId);
    } catch (error) {
      console.error('Error disconnecting:', error);
    } finally {
      setLoadingConnections(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  const handleRemove = async (connectionId: string) => {
    if (confirm(t('connections.delete.confirm'))) {
      try {
        await removeConnection(connectionId);
      } catch (error) {
        console.error('Error removing connection:', error);
      }
    }
  };

  const getStatusColor = (connection: MCPConnection) => {
    if (connection.status.connecting) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
    if (connection.status.connected) return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
    if (connection.status.error) return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
    return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
  };

  const getStatusText = (connection: MCPConnection) => {
    if (connection.status.connecting) return 'Conectando...';
    if (connection.status.connected) return 'Conectado';
    if (connection.status.error) return 'Error';
    return 'Desconectado';
  };

  const getTransportIcon = (type: string) => {
    switch (type) {
      case 'stdio':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'sse':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      case 'http':
      case 'streamable-http':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
          </svg>
        );
    }
  };

  if (connections.length === 0) {
    return (
      <div className="text-center py-16 animate-fade-in">
        <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
          <ServerIcon />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {t('connections.empty')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          {t('connections.empty.description')}
        </p>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {connections.map((connection, index) => {
        const isActive = activeConnection === connection.id;
        const isConnected = connection.status.connected;
        const isLoading = loadingConnections.has(connection.id);

        return (
          <div
            key={connection.id}
            className={`group glass-card rounded-2xl p-6 transition-all duration-300 hover-lift animate-slide-in-up ${
              isActive
                ? 'ring-2 ring-blue-500/50 bg-gradient-to-br from-blue-500/10 to-purple-500/10'
                : 'hover:bg-white/10'
            }`}
            style={{
              animationDelay: `${index * 100}ms`
            }}
          >
            {/* Header de la tarjeta */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-3 rounded-xl transition-all duration-300 ${
                  isConnected
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-gray-500/20 text-gray-400'
                }`}>
                  {getTransportIcon(connection.config.type)}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-400 transition-colors">
                    {connection.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                    {connection.config.type}
                  </p>
                </div>
              </div>

              {/* Indicador de estado */}
              <div className="flex items-center space-x-2">
                {isLoading ? (
                  <LoadingIcon />
                ) : (
                  <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    isConnected
                      ? 'bg-green-400 shadow-green-400/50 shadow-lg animate-pulse'
                      : 'bg-red-400 shadow-red-400/50 shadow-lg'
                  }`} />
                )}
                <span className={`text-xs font-semibold px-2 py-1 rounded-full ${
                  isConnected
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {isConnected ? t('connections.connected') : t('connections.disconnected')}
                </span>
              </div>
            </div>

            {/* Información de la conexión */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">{t('connections.type')}:</span>
                <span className="font-medium text-gray-900 dark:text-white capitalize">
                  {connection.config.type}
                </span>
              </div>

              {connection.config.type === 'stdio' && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">{t('form.connection.command')}:</span>
                  <span className="font-mono text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {connection.config.command}
                  </span>
                </div>
              )}

              {(connection.config.type === 'sse' || connection.config.type === 'http') && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">URL:</span>
                  <span className="font-mono text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded truncate max-w-48">
                    {connection.config.url}
                  </span>
                </div>
              )}
            </div>

            {/* Botones de acción */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {!isConnected && !connection.status.connecting && (
                  <button
                    onClick={() => handleConnect(connection.id)}
                    disabled={isLoading}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold rounded-lg transition-all duration-300 hover-lift disabled:cursor-not-allowed"
                  >
                    {isLoading ? <LoadingIcon /> : <PlayIcon />}
                    <span>{isLoading ? t('connections.connecting') : t('connections.connect')}</span>
                  </button>
                )}

                {isConnected && (
                  <>
                    <button
                      onClick={() => setActiveConnection(connection.id)}
                      className={`flex items-center space-x-2 px-4 py-2 font-semibold rounded-lg transition-all duration-300 hover-lift ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                          : 'bg-white/10 hover:bg-white/20 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      <span>{isActive ? t('connections.active') : t('connections.activate')}</span>
                    </button>
                    <button
                      onClick={() => handleDisconnect(connection.id)}
                      disabled={isLoading}
                      className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold rounded-lg transition-all duration-300 hover-lift disabled:cursor-not-allowed"
                    >
                      {isLoading ? <LoadingIcon /> : <StopIcon />}
                      <span>{isLoading ? t('connections.disconnecting') : t('connections.disconnect')}</span>
                    </button>
                  </>
              </div>

              {/* Botón de eliminar */}
              <button
                onClick={() => handleRemove(connection.id)}
                className="p-2 rounded-lg bg-red-500/10 hover:bg-red-500/20 text-red-400 hover:text-red-300 transition-all duration-300 hover-lift"
                title={t('connections.delete')}
              >
                <DeleteIcon />
              </button>
            </div>

            {/* Información adicional */}
            {isConnected && (
              <div className="mt-4 pt-4 border-t border-white/10">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-blue-400">
                      {connection.tools.length}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t('nav.tools')}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-green-400">
                      {connection.resources.length}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t('nav.resources')}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-purple-400">
                      {connection.prompts.length}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t('nav.prompts')}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Error display */}
            {connection.status.error && (
              <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <div className="flex items-start space-x-2">
                  <div className="text-red-400 mt-0.5">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-red-400">Error de conexión</p>
                    <p className="text-xs text-red-300 mt-1">{connection.status.error}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
