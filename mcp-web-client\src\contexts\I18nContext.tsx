'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

export type Language = 'es' | 'en';

interface I18nContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

// Traducciones
const translations = {
  es: {
    // Header
    'header.title': 'Cliente MCP',
    'header.subtitle': 'Protocolo de Contexto de Modelo',
    
    // Navigation
    'nav.connections': 'Conexiones',
    'nav.tools': 'Herramientas',
    'nav.resources': 'Recursos',
    'nav.prompts': 'Prompts',
    'nav.chat': 'Chat IA',
    'nav.logs': 'Logs',
    
    // Theme
    'theme.light': 'Claro',
    'theme.dark': 'Oscuro',
    'theme.system': 'Sistema',
    'theme.toggle': 'Cambiar tema',
    
    // Language
    'language.spanish': 'Español',
    'language.english': 'English',
    'language.toggle': 'Cambiar idioma',
    
    // Dashboard
    'dashboard.status': 'Estado del Cliente MCP',
    'dashboard.connections.active': '{count} de {total} conexiones activas',
    'dashboard.connection.active': 'Conexión activa: {name}',
    'dashboard.add.connection': 'Agregar Conexión',
    
    // Connections
    'connections.title': 'Gestión de Conexiones',
    'connections.add': 'Nueva Conexión',
    'connections.name': 'Nombre',
    'connections.type': 'Tipo',
    'connections.status': 'Estado',
    'connections.actions': 'Acciones',
    'connections.connect': 'Conectar',
    'connections.disconnect': 'Desconectar',
    'connections.delete': 'Eliminar',
    'connections.edit': 'Editar',
    'connections.connected': 'Conectado',
    'connections.disconnected': 'Desconectado',
    'connections.connecting': 'Conectando...',
    'connections.disconnecting': 'Desconectando...',
    'connections.active': 'Activa',
    'connections.activate': 'Activar',
    'connections.empty': 'No hay conexiones',
    'connections.empty.description': 'Crea una nueva conexión para comenzar a usar MCP.',
    
    // Connection Form
    'form.connection.title': 'Nueva Conexión MCP',
    'form.connection.name': 'Nombre de la conexión',
    'form.connection.name.placeholder': 'Mi servidor MCP',
    'form.connection.type': 'Tipo de conexión',
    'form.connection.command': 'Comando',
    'form.connection.command.placeholder': 'node server.js',
    'form.connection.args': 'Argumentos',
    'form.connection.args.placeholder': 'arg1 arg2',
    'form.connection.url': 'URL del servidor',
    'form.connection.url.placeholder': 'http://localhost:3000',
    'form.connection.save': 'Guardar Conexión',
    'form.connection.cancel': 'Cancelar',
    
    // Tools
    'tools.title': 'Herramientas Disponibles',
    'tools.empty': 'No hay herramientas disponibles',
    'tools.description': 'Descripción',
    'tools.execute': 'Ejecutar',
    'tools.parameters': 'Parámetros',
    
    // Resources
    'resources.title': 'Recursos Disponibles',
    'resources.empty': 'No hay recursos disponibles',
    'resources.uri': 'URI',
    'resources.name': 'Nombre',
    'resources.description': 'Descripción',
    'resources.mimeType': 'Tipo MIME',
    'resources.read': 'Leer',
    
    // Prompts
    'prompts.title': 'Prompts Disponibles',
    'prompts.empty': 'No hay prompts disponibles',
    'prompts.arguments': 'Argumentos',
    'prompts.use': 'Usar',
    
    // Chat
    'chat.title': 'Asistente IA',
    'chat.placeholder': 'Escribe tu mensaje aquí...',
    'chat.send': 'Enviar',
    'chat.clear': 'Limpiar chat',
    'chat.thinking': 'Pensando...',
    'chat.error': 'Error en el chat',
    'chat.retry': 'Reintentar',
    
    // Logs
    'logs.title': 'Registro de Actividad',
    'logs.clear': 'Limpiar logs',
    'logs.empty': 'No hay logs disponibles',
    'logs.timestamp': 'Hora',
    'logs.level': 'Nivel',
    'logs.message': 'Mensaje',
    
    // Common
    'common.loading': 'Cargando...',
    'common.error': 'Error',
    'common.success': 'Éxito',
    'common.warning': 'Advertencia',
    'common.info': 'Información',
    'common.close': 'Cerrar',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'common.delete': 'Eliminar',
    'common.edit': 'Editar',
    'common.view': 'Ver',
    'common.copy': 'Copiar',
    'common.copied': 'Copiado',
  },
  en: {
    // Header
    'header.title': 'MCP Client',
    'header.subtitle': 'Model Context Protocol',
    
    // Navigation
    'nav.connections': 'Connections',
    'nav.tools': 'Tools',
    'nav.resources': 'Resources',
    'nav.prompts': 'Prompts',
    'nav.chat': 'AI Chat',
    'nav.logs': 'Logs',
    
    // Theme
    'theme.light': 'Light',
    'theme.dark': 'Dark',
    'theme.system': 'System',
    'theme.toggle': 'Toggle theme',
    
    // Language
    'language.spanish': 'Español',
    'language.english': 'English',
    'language.toggle': 'Change language',
    
    // Dashboard
    'dashboard.status': 'MCP Client Status',
    'dashboard.connections.active': '{count} of {total} active connections',
    'dashboard.connection.active': 'Active connection: {name}',
    'dashboard.add.connection': 'Add Connection',
    
    // Connections
    'connections.title': 'Connection Management',
    'connections.add': 'New Connection',
    'connections.name': 'Name',
    'connections.type': 'Type',
    'connections.status': 'Status',
    'connections.actions': 'Actions',
    'connections.connect': 'Connect',
    'connections.disconnect': 'Disconnect',
    'connections.delete': 'Delete',
    'connections.edit': 'Edit',
    'connections.connected': 'Connected',
    'connections.disconnected': 'Disconnected',
    'connections.connecting': 'Connecting...',
    'connections.disconnecting': 'Disconnecting...',
    'connections.active': 'Active',
    'connections.activate': 'Activate',
    'connections.empty': 'No connections',
    'connections.empty.description': 'Create a new connection to start using MCP.',
    
    // Connection Form
    'form.connection.title': 'New MCP Connection',
    'form.connection.name': 'Connection name',
    'form.connection.name.placeholder': 'My MCP server',
    'form.connection.type': 'Connection type',
    'form.connection.command': 'Command',
    'form.connection.command.placeholder': 'node server.js',
    'form.connection.args': 'Arguments',
    'form.connection.args.placeholder': 'arg1 arg2',
    'form.connection.url': 'Server URL',
    'form.connection.url.placeholder': 'http://localhost:3000',
    'form.connection.save': 'Save Connection',
    'form.connection.cancel': 'Cancel',
    
    // Tools
    'tools.title': 'Available Tools',
    'tools.empty': 'No tools available',
    'tools.description': 'Description',
    'tools.execute': 'Execute',
    'tools.parameters': 'Parameters',
    
    // Resources
    'resources.title': 'Available Resources',
    'resources.empty': 'No resources available',
    'resources.uri': 'URI',
    'resources.name': 'Name',
    'resources.description': 'Description',
    'resources.mimeType': 'MIME Type',
    'resources.read': 'Read',
    
    // Prompts
    'prompts.title': 'Available Prompts',
    'prompts.empty': 'No prompts available',
    'prompts.arguments': 'Arguments',
    'prompts.use': 'Use',
    
    // Chat
    'chat.title': 'AI Assistant',
    'chat.placeholder': 'Type your message here...',
    'chat.send': 'Send',
    'chat.clear': 'Clear chat',
    'chat.thinking': 'Thinking...',
    'chat.error': 'Chat error',
    'chat.retry': 'Retry',
    
    // Logs
    'logs.title': 'Activity Log',
    'logs.clear': 'Clear logs',
    'logs.empty': 'No logs available',
    'logs.timestamp': 'Time',
    'logs.level': 'Level',
    'logs.message': 'Message',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Information',
    'common.close': 'Close',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.copy': 'Copy',
    'common.copied': 'Copied',
  },
};

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('es');

  // Cargar idioma guardado al inicializar
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && ['es', 'en'].includes(savedLanguage)) {
        setLanguage(savedLanguage);
      } else {
        // Detectar idioma del navegador
        const browserLanguage = navigator.language.toLowerCase();
        if (browserLanguage.startsWith('es')) {
          setLanguage('es');
        } else {
          setLanguage('en');
        }
      }
    }
  }, []);

  // Guardar idioma en localStorage
  const handleSetLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', newLanguage);
      document.documentElement.lang = newLanguage;
    }
  };

  // Función de traducción
  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[language][key as keyof typeof translations[typeof language]] || key;
    
    // Reemplazar parámetros
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        translation = translation.replace(`{${paramKey}}`, String(paramValue));
      });
    }
    
    return translation;
  };

  const value: I18nContextType = {
    language,
    setLanguage: handleSetLanguage,
    t,
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
}

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}
